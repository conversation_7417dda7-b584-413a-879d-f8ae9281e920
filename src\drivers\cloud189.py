import os
import time
import logging
from typing import Dict, Any, List, Optional
from urllib.parse import quote
from utils import utils
from utils.kv_cache import KVCache
from ._base import BaseDriver
from core.log_manager import LogManager
from core.config_manager import ConfigManager

# 导入cloud189 SDK
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), "..", ".."))
from cloud189 import Cloud189
from cloud189.store import Store

logger = LogManager.get_logger(__name__)


class ConfigStore(Store):
    """配置管理器集成的Token存储类"""

    def __init__(self, config_manager: ConfigManager, storage_name: str):
        """初始化配置存储

        Args:
            config_manager: 配置管理器实例
            storage_name: 存储名称
        """
        self.config_manager = config_manager
        self.storage_name = storage_name

    def get(self):
        """获取存储的token数据"""
        try:
            config = self.config_manager.get_storage(self.storage_name)
            if config:
                return config.get("token_data")
            return None
        except Exception as e:
            logger.error(f"获取token数据失败: {e}")
            return None

    def update(self, data):
        """更新存储的token数据"""
        try:
            config = self.config_manager.get_storage(self.storage_name)
            if config:
                config["token_data"] = data
                return self.config_manager.update_storage(self.storage_name, config)
            return False
        except Exception as e:
            logger.error(f"更新token数据失败: {e}")
            return False


class Cloud189Driver(BaseDriver):
    """天翼云盘驱动类"""

    DRIVER_TYPE = "cloud189"
    DRIVER_NAME = "天翼云盘"

    DRIVER_CONFIG = {
        "username": {
            "type": "string",
            "required": True,
            "label": "用户名",
            "tip": "天翼云盘登录用户名（手机号）",
        },
        "password": {
            "type": "password",
            "required": True,
            "label": "密码",
            "tip": "天翼云盘登录密码",
        },
        "strm_mode": {
            "type": "select",
            "required": False,
            "label": "STRM 模式",
            "tip": "选择STRM文件的播放模式",
            "options": [
                {"value": "direct", "label": "直链模式"},
                {"value": "proxy", "label": "代理模式"},
            ],
            "default": "direct",
        },
        "token_data": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "user_info": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用天翼云盘API访问<br><b>注意：登录状态会自动维护，token失效时会自动重新登录。</b>",
    }

    def __init__(self, config: Dict[str, Any]):
        """初始化天翼云盘驱动

        Args:
            config: 驱动配置
        """
        self.name = None
        self.username = config["username"].strip()
        self.password = config["password"].strip()
        self.strm_mode = config.get("strm_mode", "direct")

        # 缓存
        self.fid_cache = KVCache()
        self.fid_cache.set("/", "-11", None)  # 天翼云盘根目录ID为-11
        self.download_cache = KVCache()

        # 配置管理器
        self.config_manager = ConfigManager

        # 用户信息
        self.user_info = {}

        # Cloud189客户端（延迟初始化）
        self.client = None

    def _init_client(self):
        """初始化Cloud189客户端"""
        if self.client is None:
            # 创建配置存储
            token_store = ConfigStore(self.config_manager, self.name)

            # 初始化客户端
            options = {
                "username": self.username,
                "password": self.password,
                "token": token_store,
            }

            self.client = Cloud189(options)
            logger.info("Cloud189客户端初始化成功")

    def init_info(self) -> bool:
        """初始化用户信息"""
        try:
            self._init_client()

            # 获取用户存储空间信息
            space_info = self.client.get_disk_space_info()

            self.user_info = {
                "user_name": "天翼云盘用户",
                "user_face": "",
                "vip_level": "",
                "space_info": {
                    "total": space_info.get("totalSize", 0),
                    "remain": space_info.get("availableSize", 0),
                    "use": space_info.get("usedSize", 0),
                },
            }

            # 保存用户信息到配置
            config = self.config_manager.get_storage(self.name)
            if config:
                config["user_info"] = self.user_info
                self.config_manager.update_storage(self.name, config)

            logger.info("天翼云盘用户信息初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化用户信息失败: {e}")
            return False

    def _get_folder_id(self, path: str) -> str:
        """获取文件夹ID

        Args:
            path: 文件夹路径

        Returns:
            str: 文件夹ID
        """
        # 检查缓存
        cached_id = self.fid_cache.get(path)
        if cached_id:
            return cached_id

        # 根目录
        if path == "/" or path == "":
            return "-11"

        # 分解路径
        parts = [p for p in path.split("/") if p]
        current_id = "-11"  # 根目录ID
        current_path = "/"

        try:
            self._init_client()

            for part in parts:
                current_path = current_path.rstrip("/") + "/" + part

                # 检查缓存
                cached_id = self.fid_cache.get(current_path)
                if cached_id:
                    current_id = cached_id
                    continue

                # 获取当前目录下的文件列表
                files = self.client.get_all_files(current_id)

                # 查找目标文件夹
                found = False
                for file_info in files:
                    if file_info.get("is_folder") and file_info.get("name") == part:
                        current_id = file_info.get("id")
                        self.fid_cache.set(current_path, current_id, 3600)  # 缓存1小时
                        found = True
                        break

                if not found:
                    raise FileNotFoundError(f"文件夹不存在: {current_path}")

            return current_id

        except Exception as e:
            logger.error(f"获取文件夹ID失败: {e}")
            raise

    def list_files(self, path: str) -> Dict[str, Any]:
        """列出指定路径下的所有文件

        Args:
            path: 要扫描的路径

        Returns:
            Dict[str, Any]: 包含文件列表的响应
        """
        try:
            self._init_client()

            # 获取文件夹ID
            folder_id = self._get_folder_id(path)

            # 获取文件列表
            files = self.client.get_all_files(folder_id)

            # 格式化文件信息
            formatted_files = []
            for file_info in files:
                formatted_file = {
                    "name": file_info.get("name", ""),
                    "isdir": file_info.get("is_folder", False),
                    "path": f"{path.rstrip('/')}/{file_info.get("name", "")}",
                    "size": (
                        file_info.get("size", 0)
                        if not file_info.get("is_folder", False)
                        else 0
                    ),
                    "modified": file_info.get("modified", ""),
                    "created": file_info.get(
                        "modified", ""
                    ),  # 使用修改时间作为创建时间
                    "id": file_info.get("id", ""),
                }
                formatted_files.append(formatted_file)

            return {
                "success": True,
                "data": formatted_files,
                "message": "获取文件列表成功",
            }

        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return {"success": False, "data": [], "message": f"列出文件失败: {str(e)}"}

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            self._init_client()

            # 分解路径
            parent_path = "/".join(path.split("/")[:-1]) or "/"
            file_name = path.split("/")[-1]

            # 获取父文件夹ID
            parent_id = self._get_folder_id(parent_path)

            # 获取文件列表，找到要删除的文件
            files = self.client.get_all_files(parent_id)

            target_file = None
            for file_info in files:
                if file_info.get("name") == file_name:
                    target_file = file_info
                    break

            if not target_file:
                logger.error(f"文件不存在: {path}")
                return False

            # 删除文件
            file_id = target_file.get("id")
            is_folder = target_file.get("is_folder", False)

            self.client.delete(file_id, file_name, is_folder)

            # 清除缓存
            if is_folder:
                self.fid_cache.delete(path)

            logger.info(f"删除文件成功: {path}")
            return True

        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            # 天翼云盘SDK暂不支持重命名功能
            logger.warning("天翼云盘暂不支持重命名功能")
            return False

        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def _get_file_info(self, path: str) -> Optional[Dict[str, Any]]:
        """获取文件信息

        Args:
            path: 文件路径

        Returns:
            Optional[Dict[str, Any]]: 文件信息，如果文件不存在返回None
        """
        try:
            # 分解路径
            parent_path = "/".join(path.split("/")[:-1]) or "/"
            file_name = path.split("/")[-1]

            # 获取父文件夹ID
            parent_id = self._get_folder_id(parent_path)

            # 获取文件列表，找到目标文件
            files = self.client.get_all_files(parent_id)

            for file_info in files:
                if file_info.get("name") == file_name:
                    return file_info

            return None

        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return None

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 URL
        """
        try:
            self._init_client()

            # 检查缓存
            cache_key = f"download_{path}"
            cached_url = self.download_cache.get(cache_key)
            if cached_url:
                return cached_url

            # 获取文件信息
            if not file_info:
                file_info = self._get_file_info(path)
                if not file_info:
                    raise FileNotFoundError(f"文件不存在: {path}")

            file_id = file_info.get("id")
            if not file_id:
                raise ValueError("文件ID不存在")

            # 获取下载链接
            download_url = self.client.download(file_id)

            # 缓存下载链接（5分钟）
            self.download_cache.set(cache_key, download_url, 300)

            return download_url

        except Exception as e:
            logger.error(f"获取下载URL失败: {e}")
            return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: STRM URL
        """
        try:
            if self.strm_mode == "direct":
                # 直链模式：返回下载链接
                return self.get_download_url(path, file_info)
            else:
                # 代理模式：返回代理链接
                encoded_path = quote(path, safe="/")
                return f"/strm/{self.name}/{encoded_path}"

        except Exception as e:
            logger.error(f"获取STRM URL失败: {e}")
            return ""

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            bytes: 文件二进制数据
        """
        try:
            # 获取下载链接
            download_url = self.get_download_url(path, file_info)
            if not download_url:
                raise ValueError("无法获取下载链接")

            # 下载文件数据
            import requests

            response = requests.get(download_url, timeout=30)
            response.raise_for_status()

            return response.content

        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""

    def _test_connection(self) -> bool:
        """测试连接状态

        Returns:
            bool: 连接是否正常
        """
        try:
            self._init_client()

            # 尝试获取根目录文件列表来测试连接
            self.client.get_all_files("-11")
            return True

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    def _refresh_login(self) -> bool:
        """刷新登录状态

        Returns:
            bool: 刷新是否成功
        """
        try:
            # 重置客户端，强制重新登录
            self.client = None
            self._init_client()

            # 测试连接
            if self._test_connection():
                logger.info("登录状态刷新成功")
                return True
            else:
                logger.error("登录状态刷新失败")
                return False

        except Exception as e:
            logger.error(f"刷新登录状态失败: {e}")
            return False

    def get_play_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取视频播放URL（用于视频文件）

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 播放URL
        """
        try:
            self._init_client()

            # 获取文件信息
            if not file_info:
                file_info = self._get_file_info(path)
                if not file_info:
                    raise FileNotFoundError(f"文件不存在: {path}")

            file_id = file_info.get("id")
            if not file_id:
                raise ValueError("文件ID不存在")

            # 尝试获取播放链接
            try:
                play_url = self.client.get_play_url(file_id)
                return play_url
            except:
                # 如果获取播放链接失败，回退到下载链接
                return self.get_download_url(path, file_info)

        except Exception as e:
            logger.error(f"获取播放URL失败: {e}")
            return ""

    def get_space_info(self) -> Dict[str, Any]:
        """获取存储空间信息

        Returns:
            Dict[str, Any]: 存储空间信息
        """
        try:
            self._init_client()

            space_info = self.client.get_disk_space_info()

            return {
                "total": space_info.get("totalSize", 0),
                "used": space_info.get("usedSize", 0),
                "available": space_info.get("availableSize", 0),
            }

        except Exception as e:
            logger.error(f"获取存储空间信息失败: {e}")
            return {
                "total": 0,
                "used": 0,
                "available": 0,
            }
