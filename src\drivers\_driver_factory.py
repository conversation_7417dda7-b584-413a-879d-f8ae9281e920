from typing import Dict, Any, Optional, List
from ._base import BaseDriver
from .webdav import WebDAVDriver
from .openlist import OpenListDriver
from .local import LocalDriver
from .quark import QuarkDriver
from .open115 import Open115Driver
from .cloud189 import Cloud189Driver
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class DriverFactory:
    """驱动工厂类，用于创建不同类型的驱动实例"""

    # 所有可用的驱动类
    _DRIVERS = [OpenListDriver, WebDAVDriver, LocalDriver, QuarkDriver, Open115Driver, Cloud189Driver]

    @classmethod
    def get_available_drivers(cls) -> List[Dict[str, Any]]:
        """获取所有可用的驱动信息

        Returns:
            List[Dict[str, Any]]: 驱动信息列表，包含类型、配置定义和提示信息
        """
        drivers = []
        for driver_class in cls._DRIVERS:
            drivers.append(
                {
                    "type": driver_class.DRIVER_TYPE,
                    "name": driver_class.DRIVER_NAME,
                    "config": driver_class.DRIVER_CONFIG,
                    "tips": driver_class.DRIVER_TIPS,
                }
            )
        return drivers

    @classmethod
    def create_driver(cls, config: Dict[str, Any]) -> Optional[BaseDriver]:
        """创建驱动实例

        Args:
            config: 驱动配置

        Returns:
            Optional[BaseDriver]: 驱动实例，如果创建失败则返回 None
        """
        try:
            driver_type = config.get("driver", "").lower()
            for driver_class in cls._DRIVERS:
                if driver_class.DRIVER_TYPE == driver_type:
                    driver = driver_class(config)
                    driver.name = config["name"]
                    return driver
            logger.error(f"不支持的驱动类型: {driver_type}")
            return None
        except Exception as e:
            logger.error(f"创建驱动实例失败: {e}")
            return None

    @classmethod
    def get_oauth_url(cls, driver_type: str, **kwargs: Any) -> Optional[str]:
        """获取驱动的授权 URL

        Args:
            driver_type: 驱动类型

        Returns:
            Optional[BaseDriver]: 授权 URL，如果驱动不支持授权则返回 None
        """
        for driver_class in cls._DRIVERS:
            if driver_class.DRIVER_TYPE == driver_type:
                if hasattr(driver_class, "get_oauth_url"):
                    return driver_class.get_oauth_url(**kwargs)
        logger.error(f"驱动不支持OAuth授权: {driver_type}")
        return None

    @classmethod
    def oauth_callback(cls, driver_type: str, **kwargs: Any) -> bool:
        """OAuth 回调

        Args:
            driver_type: 驱动类型

        Returns:
            bool: 是否成功
        """
        for driver_class in cls._DRIVERS:
            if driver_class.DRIVER_TYPE == driver_type:
                if hasattr(driver_class, "oauth_callback"):
                    return driver_class.oauth_callback(**kwargs)
        logger.error(f"驱动不支持OAuth授权: {driver_type}")
        return False
